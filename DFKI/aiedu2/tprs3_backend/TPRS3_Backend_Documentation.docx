<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>TPRS 3.0 Backend - Comprehensive Documentation</title>
    <style>
        body { font-family: '<PERSON><PERSON>ri', Arial, sans-serif; margin: 1in; line-height: 1.15; }
        h1 { color: #2E75B6; font-size: 24pt; text-align: center; page-break-before: always; }
        h2 { color: #2E75B6; font-size: 18pt; border-bottom: 2px solid #2E75B6; padding-bottom: 5px; }
        h3 { color: #1F4E79; font-size: 14pt; margin-top: 20px; }
        h4 { color: #1F4E79; font-size: 12pt; margin-top: 15px; }
        .warning { background-color: #FFE6E6; border: 2px solid #FF0000; padding: 15px; margin: 10px 0; }
        .warning h2, .warning h3 { color: #CC0000; }
        .code { background-color: #F5F5F5; border: 1px solid #CCCCCC; padding: 10px; font-family: 'Courier New', monospace; font-size: 10pt; }
        .diagram { text-align: center; background-color: #F0F8FF; border: 1px solid #4682B4; padding: 15px; margin: 10px 0; }
        .toc { background-color: #F9F9F9; border: 1px solid #CCCCCC; padding: 20px; }
        .example { background-color: #F0F8F0; border-left: 4px solid #4CAF50; padding: 15px; margin: 10px 0; }
        ul, ol { margin-left: 20px; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #CCCCCC; padding: 8px; text-align: left; }
        th { background-color: #E6F3FF; font-weight: bold; }
        .restricted { background-color: #FFCCCC; color: #CC0000; font-weight: bold; }
    </style>
</head>
<body>

<h1>TPRS 3.0 Backend - Comprehensive Documentation</h1>

<div class="toc">
<h2>TABLE OF CONTENTS</h2>
<ol>
    <li><a href="#project-overview">Project Overview</a></li>
    <li><a href="#system-architecture">System Architecture</a></li>
    <li><a href="#installation-setup">Installation & Setup Guide</a></li>
    <li><a href="#api-endpoints">API Endpoints Documentation</a></li>
    <li><a href="#security-warning">⚠️ CRITICAL SECURITY WARNING - VectorDB Endpoints</a></li>
    <li><a href="#core-components">Core Components</a></li>
    <li><a href="#database-architecture">Database Architecture</a></li>
    <li><a href="#german-examples">German Use Case Examples</a></li>
    <li><a href="#development-workflow">Development Workflow</a></li>
    <li><a href="#troubleshooting">Troubleshooting</a></li>
    <li><a href="#deployment-guide">Deployment Guide</a></li>
    <li><a href="#maintenance-monitoring">Maintenance & Monitoring</a></li>
</ol>
</div>

<h2 id="project-overview">1. PROJECT OVERVIEW</h2>

<h3>1.1 What is TPRS 3.0?</h3>
<p>TPRS 3.0 (Term Paper Recommendation System) is an AI-powered backend system designed to assist students and researchers in developing high-quality research topics and questions. The system provides intelligent feedback, sentiment analysis, and reference recommendations using advanced Large Language Models (LLMs) and vector database technology.</p>

<h3>1.2 Key Features</h3>
<ul>
    <li><strong>Sentiment Analysis:</strong> Analyzes research topics and questions to determine emotional tone</li>
    <li><strong>Research Topic Validation:</strong> Provides feedback on research topic relevance and quality</li>
    <li><strong>Research Question Assessment:</strong> Evaluates research questions for clarity and scope</li>
    <li><strong>Reference Generation:</strong> Automatically generates relevant academic references</li>
    <li><strong>LLM-powered Feedback:</strong> Comprehensive feedback using multiple LLM models</li>
    <li><strong>Chain of Thought:</strong> Transparent reasoning process for all recommendations</li>
    <li><strong>Vector Database Integration:</strong> Semantic search capabilities for document retrieval</li>
    <li><strong>Multi-language Support:</strong> Primarily German with English support</li>
</ul>

<h3>1.3 Technology Stack</h3>
<ul>
    <li><strong>Backend Framework:</strong> FastAPI (Python 3.11)</li>
    <li><strong>Database:</strong> PostgreSQL with pgvector extension</li>
    <li><strong>Vector Embeddings:</strong> Ollama embeddings via FernUni server</li>
    <li><strong>LLM Integration:</strong> Langchain with multiple model support</li>
    <li><strong>Containerization:</strong> Docker & Docker Compose</li>
    <li><strong>Logging:</strong> Loguru for structured logging</li>
    <li><strong>API Documentation:</strong> Automatic OpenAPI/Swagger documentation</li>
</ul>

<h2 id="system-architecture">2. SYSTEM ARCHITECTURE</h2>

<h3>2.1 High-Level Architecture</h3>
<div class="diagram">
    <p><strong>TPRS3 System Architecture Diagram</strong></p>
    <p>The system follows a layered architecture with the following components:</p>
    <table>
        <tr><th>Layer</th><th>Components</th><th>Description</th></tr>
        <tr><td>Client Layer</td><td>Frontend Applications</td><td>React/Vue/Angular applications that consume the API</td></tr>
        <tr><td>API Gateway</td><td>FastAPI Backend</td><td>Main entry point serving on port 8000</td></tr>
        <tr><td>Service Layer</td><td>LLM, VectorDB, MongoDB Endpoints</td><td>Three main endpoint groups with different access levels</td></tr>
        <tr><td>Processing Layer</td><td>AI Utilities</td><td>Sentiment analysis, feedback generation, references</td></tr>
        <tr><td>External Services</td><td>Ollama LLM Server</td><td>FernUni Hagen server on port 11434</td></tr>
        <tr><td>Database Layer</td><td>PostgreSQL + pgvector, MongoDB</td><td>Vector operations and user data storage</td></tr>
        <tr><td>Storage Layer</td><td>Knowledge Base, Logs</td><td>Document storage and structured logging</td></tr>
    </table>
</div>

<h3>2.2 API Request Flow</h3>
<div class="diagram">
    <p><strong>API Request Processing Flow</strong></p>
    <ol>
        <li><strong>Request Reception:</strong> FastAPI receives and validates incoming requests</li>
        <li><strong>Logging:</strong> All requests are logged with structured information</li>
        <li><strong>Processing:</strong> Business logic utilities process the request</li>
        <li><strong>LLM Integration:</strong> External Ollama server provides AI capabilities</li>
        <li><strong>Database Operations:</strong> Vector searches and data persistence as needed</li>
        <li><strong>Response Formation:</strong> Structured responses with proper error handling</li>
    </ol>
</div>

2.3 Container Architecture

[DIAGRAM: Container Deployment Architecture]

The system runs in a Docker-managed environment with two main services:

1. pgvector-db: PostgreSQL database with vector extension
   - Image: pgvector/pgvector:pg16
   - Port: 6024:5432
   - Persistent storage via Docker volumes

2. backend: FastAPI application server
   - Built from custom Dockerfile
   - Port: 8000:8000
   - Hot-reload enabled in development mode

2.4 Directory Structure

tprs3_backend/
├── backend/                    # Main application code
│   ├── api/                   # API endpoint definitions
│   ├── models/                # Pydantic data models
│   ├── utils/                 # Utility functions
│   ├── vector_db/            # Vector database operations
│   ├── knowledge_base/       # Document storage
│   ├── logs/                 # Application logs
│   └── main.py               # FastAPI application entry point
├── docs/                     # Documentation and deployment guides
├── pocs/                     # Proof of concepts and experiments
├── docker-compose.dev.yml    # Development environment
├── docker-compose.prod.yml   # Production environment
└── start-dev.sh             # Development startup script

═══════════════════════════════════════════════════════════════════════════════

3. INSTALLATION & SETUP GUIDE

3.1 Prerequisites

Before setting up TPRS 3.0, ensure you have:
• Docker: Version 20.0 or higher
• Docker Compose: Version 2.0 or higher
• Git: For repository cloning
• Minimum System Requirements:
  - RAM: 8GB (16GB recommended)
  - Storage: 10GB free space
  - CPU: Multi-core processor recommended

3.2 Step-by-Step Installation

Step 1: Clone the Repository
<NAME_EMAIL>:edtec/aiedu2/tprs3_backend.git
cd tprs3_backend

Step 2: Environment Configuration
Create a .env file in the backend/ directory:

# backend/.env

# PostgreSQL Configuration
DATABASE_NAME=langchain
DATABASE_USER=langchain
DATABASE_PASSWORD=langchain
DATABASE_HOST=pgvector-db
DATABASE_PORT=5432

# Ollama Server Configuration
FERN_UNI_OLLAMA_SERVER_URL=http://catalpa-llm.fernuni-hagen.de:11434/

# Optional: Collection names for vector database
COLLECTION_NAME=dfki_docs

Step 3: Make Scripts Executable
chmod +x start-dev.sh
chmod +x start-prod.sh
chmod +x vector_db_dev.sh
chmod +x vector_db_prod.sh

Step 4: Start the Development Environment
./start-dev.sh

This script will:
- Stop any existing containers
- Build the FastAPI backend image
- Start PostgreSQL with pgvector
- Launch the FastAPI server with hot-reload

Step 5: Initialize Vector Database
After containers are running, populate the vector database:
./vector_db_dev.sh

NOTE: This process takes 6-10 minutes depending on system performance and internet speed.

3.3 Verification

1. Check running containers:
   docker ps
   You should see pgvector-container and fastapi-backend-server

2. Access API Documentation:
   Open http://localhost:8000/docs

3. Test health endpoint:
   curl http://localhost:8000/health

═══════════════════════════════════════════════════════════════════════════════

4. API ENDPOINTS DOCUMENTATION

4.1 Core LLM Endpoints (/api)

4.1.1 Sentiment Analysis
Endpoint: POST /api/sentiment
Purpose: Analyzes the emotional tone of research topics and questions

Request Model:
{
  "researchTopic": "string",
  "researchQuestions": [
    {
      "value": "string"
    }
  ]
}

Response Model:
{
  "sentiment": "positive|negative|neutral",
  "reason": "string",
  "recommendedEngine": "knowledge_based|expert_based|no_recommendation",
  "timestamp": "string"
}

Business Logic:
- Combines research topic and questions into analysis string
- Uses LLM to determine sentiment (positive/negative/neutral)
- Recommends appropriate engine based on sentiment
- Returns reasoning for transparency

4.1.2 Research Topic Feedback
Endpoint: POST /api/research_topic
Purpose: Validates research topic relevance and quality

Request Model:
{
  "researchTopic": "string",
  "personal_research_interest": "string",
  "summary_of_course": "string",
  "llmModel": "string"
}

Response Model:
{
  "researchTopic": "string",
  "validate": "boolean",
  "reason": "string",
  "timestamp": "string"
}

4.1.3 Research Question Feedback
Endpoint: POST /api/research_question
Purpose: Evaluates individual research questions

4.1.4 References Generation
Endpoint: POST /api/references
Purpose: Generates relevant academic references

Response Model:
{
  "references_markdown": "string",
  "references_normal": "string"
}

4.1.5 LLM Feedback
Endpoint: POST /api/llm_feedback
Purpose: Comprehensive feedback using vector database retrieval

Key Features:
- Uses recommendation engine (knowledge_based/expert_based)
- Retrieves relevant documents from vector database
- Generates detailed feedback with token usage tracking
- Supports multiple LLM models

4.1.6 Chain of Thought
Endpoint: POST /api/chain_of_thought
Purpose: Provides transparent reasoning process

Response: Detailed markdown explaining the complete decision-making process

4.2 Vector Database Endpoints (/vectordb)

4.2.1 Database Information
Endpoint: GET /vectordb/fetch_vectordb_info
Purpose: Retrieves information about all vector database collections

4.2.2 Collection Data
Endpoint: POST /vectordb/fetch_collection_data
Purpose: Gets detailed information about a specific collection

4.2.3 Document Retrieval
Endpoint: POST /vectordb/fetch_relevant_docs
Purpose: Performs semantic search for relevant documents

Request Model:
{
  "query": "string",
  "collection": "string",
  "top_k": "integer"
}

4.2.4 Document Upload
Endpoint: POST /vectordb/upload_pdf
Purpose: Uploads and processes PDF documents into vector database

<h4>4.3 MongoDB Endpoints (/mongodb)</h4>
<p>These endpoints handle persistent data storage for user interactions and system logs.</p>

<div class="warning">
<h2 id="security-warning">⚠️ CRITICAL SECURITY WARNING - VECTORDB ENDPOINTS</h2>

<h3>🚨 IMPORTANT: DO NOT USE VECTORDB ENDPOINTS IN PRODUCTION</h3>
<p><strong>The VectorDB endpoints (/vectordb/*) are STRICTLY FOR INTERNAL USE ONLY and should NEVER be exposed to frontend applications or external clients.</strong></p>

<div class="diagram">
    <p><strong>Vector Database Processing Pipeline</strong></p>
    <p>The vector database processes documents through a secure pipeline that should only be accessed internally.</p>
</div>

<h3>5.1 Why These Endpoints Are Restricted</h3>

<h4>5.1.1 Security Risks</h4>
<ul>
    <li><strong>Direct Database Access:</strong> These endpoints provide direct access to the vector database</li>
    <li><strong>Data Manipulation:</strong> Allow uploading and modifying core system documents</li>
    <li><strong>System Integrity:</strong> Can corrupt the knowledge base if misused</li>
    <li><strong>Performance Impact:</strong> Unrestricted access can overload the system</li>
</ul>

<h4>5.1.2 Restricted Endpoints</h4>
<div class="code">
<span class="restricted">❌ POST /vectordb/upload_pdf</span>          - PDF document upload<br>
<span class="restricted">❌ POST /vectordb/fetch_collection_data</span> - Collection information<br>
<span class="restricted">❌ GET  /vectordb/fetch_vectordb_info</span>  - Database information<br>
<span class="restricted">❌ POST /vectordb/fetch_relevant_docs</span>  - Document retrieval
</div>

<h3>5.2 Proper Usage Guidelines</h3>

<h4>5.2.1 For System Administrators Only</h4>
<ul>
    <li>Use only for initial system setup</li>
    <li>Document management during maintenance</li>
    <li>Debugging and troubleshooting purposes</li>
    <li><strong>Never expose to frontend applications</strong></li>
</ul>

<h4>5.2.2 Alternative Approaches</h4>
<p>Instead of using VectorDB endpoints directly:</p>
<ul>
    <li>Use <code>/api/references</code> for document retrieval</li>
    <li>Use <code>/api/llm_feedback</code> for context-aware responses</li>
    <li>All vector operations are handled internally by the LLM endpoints</li>
</ul>

<h3>5.3 Implementation Notes</h3>
<p>The VectorDB endpoints are available in the API documentation but should be:</p>
<ul>
    <li>Blocked at the API gateway level in production</li>
    <li>Protected with additional authentication</li>
    <li>Monitored for unauthorized access attempts</li>
    <li>Used only by authorized system administrators</li>
</ul>
</div>

<h2 id="german-examples">8. GERMAN USE CASE EXAMPLES</h2>

<p>This section provides detailed examples of how to use the TPRS3 API with German academic content, which is the primary language for the system.</p>

<h3>8.1 Complete Workflow Example: Educational Technology Research</h3>

<div class="example">
<h4>8.1.1 Sentiment Analysis Example</h4>
<p><strong>Scenario:</strong> A student wants to research "Künstliche Intelligenz in der Bildung" (Artificial Intelligence in Education)</p>

<p><strong>Request:</strong></p>
<div class="code">
POST /api/sentiment
{
  "researchTopic": "Künstliche Intelligenz in der Bildung: Chancen und Herausforderungen für das deutsche Schulsystem",
  "researchQuestions": [
    {
      "value": "Wie kann KI-basierte Lernsoftware die individuelle Förderung von Schülern verbessern?"
    },
    {
      "value": "Welche ethischen Bedenken entstehen beim Einsatz von KI-Systemen in deutschen Klassenzimmern?"
    },
    {
      "value": "Wie bereiten deutsche Lehrkräfte sich auf die Integration von KI-Tools vor?"
    }
  ]
}
</div>

<p><strong>Expected Response:</strong></p>
<div class="code">
{
  "sentiment": "positive",
  "reason": "Das Forschungsthema zeigt eine ausgewogene Betrachtung von Chancen und Herausforderungen. Die Forschungsfragen sind spezifisch auf den deutschen Kontext ausgerichtet und decken technische, ethische und praktische Aspekte ab.",
  "recommendedEngine": "knowledge_based",
  "timestamp": "2025-07-22T10:30:00Z"
}
</div>
</div>

<div class="example">
<h4>8.1.2 Research Topic Validation Example</h4>
<p><strong>Request:</strong></p>
<div class="code">
POST /api/research_topic
{
  "researchTopic": "Künstliche Intelligenz in der Bildung: Chancen und Herausforderungen für das deutsche Schulsystem",
  "personal_research_interest": "Ich interessiere mich für die Digitalisierung im Bildungswesen und möchte verstehen, wie neue Technologien das Lernen verbessern können.",
  "summary_of_course": "Modul: Bildungstechnologie und E-Learning. Schwerpunkte: Digitale Lernumgebungen, Adaptive Lernsysteme, Mediendidaktik im 21. Jahrhundert.",
  "llmModel": "llama3.1:8b"
}
</div>

<p><strong>Expected Response:</strong></p>
<div class="code">
{
  "researchTopic": "Künstliche Intelligenz in der Bildung: Chancen und Herausforderungen für das deutsche Schulsystem",
  "validate": true,
  "reason": "Das Thema ist hochrelevant und passt ausgezeichnet zu Ihren Interessen und dem Kursinhalt. Es verbindet aktuelle technologische Entwicklungen mit praktischen Bildungsanwendungen und berücksichtigt den spezifischen deutschen Kontext.",
  "timestamp": "2025-07-22T10:35:00Z"
}
</div>
</div>

<h3>8.2 Common German Academic Phrases and Terminology</h3>
<p>The system is optimized to understand and respond to common German academic language:</p>

<h4>8.2.1 Research Topic Formulations</h4>
<ul>
    <li><strong>"Analyse der..."</strong> (Analysis of...)</li>
    <li><strong>"Einfluss von... auf..."</strong> (Influence of... on...)</li>
    <li><strong>"Vergleichende Untersuchung..."</strong> (Comparative study...)</li>
    <li><strong>"Entwicklung eines Konzepts für..."</strong> (Development of a concept for...)</li>
    <li><strong>"Empirische Studie zu..."</strong> (Empirical study on...)</li>
</ul>

<h4>8.2.2 Research Question Patterns</h4>
<ul>
    <li><strong>"Wie wirkt sich... aus?"</strong> (How does... affect?)</li>
    <li><strong>"Welche Faktoren beeinflussen...?"</strong> (Which factors influence...?)</li>
    <li><strong>"Inwiefern unterscheidet sich...?"</strong> (To what extent does... differ?)</li>
    <li><strong>"Welche Rolle spielt... bei...?"</strong> (What role does... play in...?)</li>
</ul>

<h4>8.2.3 Academic Context Terms</h4>
<ul>
    <li><strong>"Hausarbeit"</strong> (Term paper)</li>
    <li><strong>"Abschlussarbeit"</strong> (Thesis)</li>
    <li><strong>"Forschungsinteresse"</strong> (Research interest)</li>
    <li><strong>"Modulbeschreibung"</strong> (Course description)</li>
    <li><strong>"Literaturrecherche"</strong> (Literature review)</li>
</ul>

<h2 id="development-workflow">9. DEVELOPMENT WORKFLOW</h2>

<h3>9.1 Local Development Setup</h3>
<ol>
    <li><strong>Start development environment:</strong>
        <div class="code">./start-dev.sh</div>
    </li>
    <li><strong>Monitor logs:</strong>
        <div class="code">
        docker logs -f fastapi-backend-server<br>
        docker logs -f pgvector-container
        </div>
    </li>
    <li><strong>Access interactive API docs:</strong>
        <ul>
            <li>Swagger UI: <a href="http://localhost:8000/docs">http://localhost:8000/docs</a></li>
            <li>ReDoc: <a href="http://localhost:8000/redoc">http://localhost:8000/redoc</a></li>
        </ul>
    </li>
</ol>

<h3>9.2 Code Structure Guidelines</h3>
<ul>
    <li><strong>Models:</strong> Define all data structures using Pydantic</li>
    <li><strong>Endpoints:</strong> Keep endpoint logic minimal, delegate to utilities</li>
    <li><strong>Utils:</strong> Implement business logic in utility functions</li>
    <li><strong>Logging:</strong> Use structured logging with appropriate levels</li>
    <li><strong>Error Handling:</strong> Implement comprehensive exception handling</li>
</ul>

<h2 id="troubleshooting">10. TROUBLESHOOTING</h2>

<h3>10.1 Common Issues</h3>

<h4>Container Startup Issues</h4>
<p><strong>Problem:</strong> Containers fail to start</p>
<p><strong>Solutions:</strong></p>
<ul>
    <li>Check port availability: <code>netstat -tulpn | grep :8000</code></li>
    <li>Verify Docker daemon is running: <code>docker info</code></li>
    <li>Check disk space: <code>df -h</code></li>
    <li>Review container logs: <code>docker logs &lt;container_name&gt;</code></li>
</ul>

<h4>Database Connection Issues</h4>
<p><strong>Problem:</strong> Cannot connect to PostgreSQL</p>
<p><strong>Solutions:</strong></p>
<ul>
    <li>Verify container is running: <code>docker ps</code></li>
    <li>Check environment variables in <code>.env</code> file</li>
    <li>Test connection: <code>docker exec -it pgvector-container psql -U langchain -d langchain</code></li>
    <li>Restart containers: <code>docker compose down && ./start-dev.sh</code></li>
</ul>

<h2 id="deployment-guide">11. DEPLOYMENT GUIDE</h2>

<h3>11.1 Production Deployment</h3>
<ol>
    <li><strong>Environment Setup:</strong>
        <div class="code">./start-prod.sh</div>
    </li>
    <li><strong>Environment Variables:</strong>
        <ul>
            <li>Update <code>.env</code> with production values</li>
            <li>Use secure passwords and connection strings</li>
            <li>Configure external LLM endpoints</li>
        </ul>
    </li>
    <li><strong>Security Considerations:</strong>
        <ul>
            <li>Enable HTTPS/TLS encryption</li>
            <li>Implement authentication and authorization</li>
            <li>Configure firewall rules</li>
            <li>Regular security updates</li>
        </ul>
    </li>
</ol>

<h2 id="maintenance-monitoring">12. MAINTENANCE & MONITORING</h2>

<h3>12.1 Regular Maintenance Tasks</h3>
<ul>
    <li><strong>Log Cleanup:</strong> Rotate and archive log files</li>
    <li><strong>Database Maintenance:</strong> Vacuum and analyze PostgreSQL</li>
    <li><strong>Vector Index Optimization:</strong> Rebuild indexes periodically</li>
    <li><strong>Dependency Updates:</strong> Keep Python packages updated</li>
    <li><strong>Security Patches:</strong> Apply system and container updates</li>
</ul>

<h3>12.2 Monitoring Metrics</h3>
<ul>
    <li><strong>API Response Times:</strong> Monitor endpoint performance</li>
    <li><strong>Database Performance:</strong> Track query execution times</li>
    <li><strong>Resource Usage:</strong> Monitor CPU, memory, and disk usage</li>
    <li><strong>Error Rates:</strong> Track and analyze error patterns</li>
    <li><strong>User Activity:</strong> Monitor API usage patterns</li>
</ul>

<hr>

<h2>CONCLUSION</h2>
<p>This documentation provides a comprehensive guide for understanding, deploying, and maintaining the TPRS 3.0 backend system. The modular architecture and well-documented APIs make it suitable for both development and production environments.</p>

<p>For additional support or questions, contact the development team or refer to the inline code documentation and API schemas available at <code>/docs</code> when the server is running.</p>

<p><strong>Developer:</strong> Rahul Bhoyar (<EMAIL>)<br>
<strong>Version:</strong> 3.0.0<br>
<strong>Last Updated:</strong> 2025-07-22</p>

</body>
</html>
