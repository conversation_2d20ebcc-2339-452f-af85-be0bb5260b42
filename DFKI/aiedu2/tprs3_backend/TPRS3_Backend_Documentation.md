# TPRS 3.0 Backend - Comprehensive Documentation

## Table of Contents
1. [Project Overview](#project-overview)
2. [System Architecture](#system-architecture)
3. [Installation & Setup Guide](#installation--setup-guide)
4. [API Endpoints Documentation](#api-endpoints-documentation)
5. [⚠️ CRITICAL SECURITY WARNING - VectorDB Endpoints](#critical-security-warning---vectordb-endpoints)
6. [Core Components](#core-components)
7. [Database Architecture](#database-architecture)
8. [German Use Case Examples](#german-use-case-examples)
9. [Development Workflow](#development-workflow)
10. [Troubleshooting](#troubleshooting)
11. [Deployment Guide](#deployment-guide)
12. [Maintenance & Monitoring](#maintenance--monitoring)

---

## 1. Project Overview

### 1.1 What is TPRS 3.0?
TPRS 3.0 (Term Paper Recommendation System) is an AI-powered backend system designed to assist students and researchers in developing high-quality research topics and questions. The system provides intelligent feedback, sentiment analysis, and reference recommendations using advanced Large Language Models (LLMs) and vector database technology.

### 1.2 Key Features
- **Sentiment Analysis**: Analyzes research topics and questions to determine emotional tone
- **Research Topic Validation**: Provides feedback on research topic relevance and quality
- **Research Question Assessment**: Evaluates research questions for clarity and scope
- **Reference Generation**: Automatically generates relevant academic references
- **LLM-powered Feedback**: Comprehensive feedback using multiple LLM models
- **Chain of Thought**: Transparent reasoning process for all recommendations
- **Vector Database Integration**: Semantic search capabilities for document retrieval
- **Multi-language Support**: Primarily German with English support

### 1.3 Technology Stack
- **Backend Framework**: FastAPI (Python 3.11)
- **Database**: PostgreSQL with pgvector extension
- **Vector Embeddings**: Ollama embeddings via FernUni server
- **LLM Integration**: Langchain with multiple model support
- **Containerization**: Docker & Docker Compose
- **Logging**: Loguru for structured logging
- **API Documentation**: Automatic OpenAPI/Swagger documentation

---

## 2. System Architecture

### 2.1 High-Level Architecture

![TPRS3 System Architecture](diagrams/system_architecture.png)

The TPRS3 system follows a layered architecture pattern with clear separation of concerns:

**Client Layer**: Frontend applications (React/Vue/Angular) that consume the API
**API Gateway Layer**: FastAPI backend serving as the main entry point
**Service Layer**: Three main endpoint groups with different access levels
**Data Processing Layer**: Core business logic and AI processing utilities
**External Services**: Ollama LLM server hosted at FernUni Hagen
**Database Layer**: PostgreSQL with pgvector for vector operations and MongoDB for user data
**Storage Layer**: Knowledge base documents and structured logging

### 2.2 API Request Flow

![API Request Flow](diagrams/api_request_flow.png)

The system processes requests through a standardized flow:
1. **Request Reception**: FastAPI receives and validates incoming requests
2. **Logging**: All requests are logged with structured information
3. **Processing**: Business logic utilities process the request
4. **LLM Integration**: External Ollama server provides AI capabilities
5. **Database Operations**: Vector searches and data persistence as needed
6. **Response Formation**: Structured responses with proper error handling

### 2.3 Container Architecture

![Container Deployment Architecture](diagrams/container_deployment.png)

The system runs in a Docker-managed environment with two main services:

1. **pgvector-db**: PostgreSQL database with vector extension
   - Image: `pgvector/pgvector:pg16`
   - Port: `6024:5432`
   - Persistent storage via Docker volumes

2. **backend**: FastAPI application server
   - Built from custom Dockerfile
   - Port: `8000:8000`
   - Hot-reload enabled in development mode

### 2.4 Directory Structure
```
tprs3_backend/
├── backend/                    # Main application code
│   ├── api/                   # API endpoint definitions
│   ├── models/                # Pydantic data models
│   ├── utils/                 # Utility functions
│   ├── vector_db/            # Vector database operations
│   ├── knowledge_base/       # Document storage
│   ├── logs/                 # Application logs
│   └── main.py               # FastAPI application entry point
├── docs/                     # Documentation and deployment guides
├── pocs/                     # Proof of concepts and experiments
├── docker-compose.dev.yml    # Development environment
├── docker-compose.prod.yml   # Production environment
└── start-dev.sh             # Development startup script
```

---

## 3. Installation & Setup Guide

### 3.1 Prerequisites
Before setting up TPRS 3.0, ensure you have:
- **Docker**: Version 20.0 or higher
- **Docker Compose**: Version 2.0 or higher
- **Git**: For repository cloning
- **Minimum System Requirements**:
  - RAM: 8GB (16GB recommended)
  - Storage: 10GB free space
  - CPU: Multi-core processor recommended

### 3.2 Step-by-Step Installation

#### Step 1: Clone the Repository
```bash
<NAME_EMAIL>:edtec/aiedu2/tprs3_backend.git
cd tprs3_backend
```

#### Step 2: Environment Configuration
Create a `.env` file in the `backend/` directory:

```ini
# backend/.env

# PostgreSQL Configuration
DATABASE_NAME=langchain
DATABASE_USER=langchain
DATABASE_PASSWORD=langchain
DATABASE_HOST=pgvector-db
DATABASE_PORT=5432

# Ollama Server Configuration
FERN_UNI_OLLAMA_SERVER_URL=http://catalpa-llm.fernuni-hagen.de:11434/

# Optional: Collection names for vector database
COLLECTION_NAME=dfki_docs
```

#### Step 3: Make Scripts Executable
```bash
chmod +x start-dev.sh
chmod +x start-prod.sh
chmod +x vector_db_dev.sh
chmod +x vector_db_prod.sh
```

#### Step 4: Start the Development Environment
```bash
./start-dev.sh
```

This script will:
- Stop any existing containers
- Build the FastAPI backend image
- Start PostgreSQL with pgvector
- Launch the FastAPI server with hot-reload

#### Step 5: Initialize Vector Database
After containers are running, populate the vector database:
```bash
./vector_db_dev.sh
```

**Note**: This process takes 6-10 minutes depending on system performance and internet speed.

### 3.3 Verification
1. **Check running containers**:
   ```bash
   docker ps
   ```
   You should see `pgvector-container` and `fastapi-backend-server`

2. **Access API Documentation**:
   Open [http://localhost:8000/docs](http://localhost:8000/docs)

3. **Test health endpoint**:
   ```bash
   curl http://localhost:8000/health
   ```

---

## 4. API Endpoints Documentation

### 4.1 Core LLM Endpoints (`/api`)

#### 4.1.1 Sentiment Analysis
**Endpoint**: `POST /api/sentiment`
**Purpose**: Analyzes the emotional tone of research topics and questions

**Request Model**:
```json
{
  "researchTopic": "string",
  "researchQuestions": [
    {
      "value": "string"
    }
  ]
}
```

**Response Model**:
```json
{
  "sentiment": "positive|negative|neutral",
  "reason": "string",
  "recommendedEngine": "knowledge_based|expert_based|no_recommendation",
  "timestamp": "string"
}
```

**Business Logic**:
- Combines research topic and questions into analysis string
- Uses LLM to determine sentiment (positive/negative/neutral)
- Recommends appropriate engine based on sentiment
- Returns reasoning for transparency

#### 4.1.2 Research Topic Feedback
**Endpoint**: `POST /api/research_topic`
**Purpose**: Validates research topic relevance and quality

**Request Model**:
```json
{
  "researchTopic": "string",
  "personal_research_interest": "string",
  "summary_of_course": "string",
  "llmModel": "string"
}
```

**Response Model**:
```json
{
  "researchTopic": "string",
  "validate": "boolean",
  "reason": "string",
  "timestamp": "string"
}
```

#### 4.1.3 Research Question Feedback
**Endpoint**: `POST /api/research_question`
**Purpose**: Evaluates individual research questions

**Request Model**:
```json
{
  "researchTopic": "string",
  "researchQuestion": "string",
  "personal_research_interest": "string",
  "summary_of_course": "string",
  "llmModel": "string"
}
```

#### 4.1.4 References Generation
**Endpoint**: `POST /api/references`
**Purpose**: Generates relevant academic references

**Response Model**:
```json
{
  "references_markdown": "string",
  "references_normal": "string"
}
```

#### 4.1.5 LLM Feedback
**Endpoint**: `POST /api/llm_feedback`
**Purpose**: Comprehensive feedback using vector database retrieval

**Key Features**:
- Uses recommendation engine (knowledge_based/expert_based)
- Retrieves relevant documents from vector database
- Generates detailed feedback with token usage tracking
- Supports multiple LLM models

#### 4.1.6 Chain of Thought
**Endpoint**: `POST /api/chain_of_thought`
**Purpose**: Provides transparent reasoning process

**Response**: Detailed markdown explaining the complete decision-making process

### 4.2 Vector Database Endpoints (`/vectordb`)

#### 4.2.1 Database Information
**Endpoint**: `GET /vectordb/fetch_vectordb_info`
**Purpose**: Retrieves information about all vector database collections

#### 4.2.2 Collection Data
**Endpoint**: `POST /vectordb/fetch_collection_data`
**Purpose**: Gets detailed information about a specific collection

#### 4.2.3 Document Retrieval
**Endpoint**: `POST /vectordb/fetch_relevant_docs`
**Purpose**: Performs semantic search for relevant documents

**Request Model**:
```json
{
  "query": "string",
  "collection": "string",
  "top_k": "integer"
}
```

#### 4.2.4 Document Upload
**Endpoint**: `POST /vectordb/upload_pdf`
**Purpose**: Uploads and processes PDF documents into vector database

### 4.3 MongoDB Endpoints (`/mongodb`)
These endpoints handle persistent data storage for user interactions and system logs.

---

## 5. ⚠️ CRITICAL SECURITY WARNING - VectorDB Endpoints

### 🚨 IMPORTANT: DO NOT USE VECTORDB ENDPOINTS IN PRODUCTION

The VectorDB endpoints (`/vectordb/*`) are **STRICTLY FOR INTERNAL USE ONLY** and should **NEVER** be exposed to frontend applications or external clients.

![Vector Database Processing Pipeline](diagrams/vector_db_pipeline.png)

### 5.1 Why These Endpoints Are Restricted

#### 5.1.1 Security Risks
- **Direct Database Access**: These endpoints provide direct access to the vector database
- **Data Manipulation**: Allow uploading and modifying core system documents
- **System Integrity**: Can corrupt the knowledge base if misused
- **Performance Impact**: Unrestricted access can overload the system

#### 5.1.2 Restricted Endpoints
```
❌ POST /vectordb/upload_pdf          - PDF document upload
❌ POST /vectordb/fetch_collection_data - Collection information
❌ GET  /vectordb/fetch_vectordb_info  - Database information
❌ POST /vectordb/fetch_relevant_docs  - Document retrieval
```

### 5.2 Proper Usage Guidelines

#### 5.2.1 For System Administrators Only
- Use only for initial system setup
- Document management during maintenance
- Debugging and troubleshooting purposes
- Never expose to frontend applications

#### 5.2.2 Alternative Approaches
Instead of using VectorDB endpoints directly:
- Use `/api/references` for document retrieval
- Use `/api/llm_feedback` for context-aware responses
- All vector operations are handled internally by the LLM endpoints

### 5.3 Implementation Notes
The VectorDB endpoints are available in the API documentation but should be:
- Blocked at the API gateway level in production
- Protected with additional authentication
- Monitored for unauthorized access attempts
- Used only by authorized system administrators

---

## 6. Core Components

### 5.1 Models (`/backend/models/`)
Pydantic models define the data structures for API requests and responses:

- **sentiment_analysis.py**: Sentiment analysis request/response models
- **rt_feedback.py**: Research topic feedback models
- **rq_feedback.py**: Research question feedback models
- **references.py**: Reference generation models
- **llm_feedback.py**: LLM feedback models
- **chain_of_thought.py**: Chain of thought models
- **vector_db.py**: Vector database operation models

### 5.2 Utilities (`/backend/utils/`)
Core business logic and helper functions:

- **sentiment.py**: Sentiment analysis logic using LLM
- **engine_recommender.py**: Recommendation engine selection
- **research_topic_feedback.py**: Research topic validation logic
- **research_question_feedback.py**: Research question evaluation
- **references.py**: Reference generation from vector database
- **llm_feedback.py**: Main LLM feedback generation
- **string_constructor.py**: Input string formatting for LLM prompts
- **prompts.py**: LLM prompt templates
- **timestamp.py**: Timestamp generation utilities

### 5.3 Vector Database (`/backend/vector_db/`)
Vector database operations for semantic search:

- **insertion.py**: Document insertion and indexing
- **retrieval.py**: Similarity search and document retrieval
- **content.py**: Database content management
- **db.py**: Database connection and configuration

### 5.4 Knowledge Base (`/backend/knowledge_base/`)
Document storage organized by recommendation engine:

- **criteria-based/**: Documents for criteria-based recommendations
- **expert-based/**: Expert knowledge documents
- **knowledge-based/**: Academic references and research papers

---

## 6. Database Architecture

### 6.1 PostgreSQL with pgvector
The system uses PostgreSQL with the pgvector extension for storing and querying vector embeddings.

**Key Collections**:
- `criteria_based_docs`: Documents for criteria-based engine
- `knowledge_based_docs`: Documents for knowledge-based engine
- `expert_based_docs`: Expert knowledge documents

**Vector Operations**:
- Embedding generation using Ollama models
- Cosine similarity search
- Metadata filtering
- Batch operations for large document sets

### 6.2 Document Processing Pipeline
1. **PDF Loading**: Extract text from PDF documents
2. **Text Cleaning**: Remove null characters and formatting issues
3. **Chunking**: Split documents into manageable chunks (1000 chars, 100 overlap)
4. **Embedding Generation**: Create vector embeddings using Ollama
5. **Storage**: Store in PostgreSQL with metadata
6. **Indexing**: Create vector indexes for fast retrieval

---

## 8. German Use Case Examples

This section provides detailed examples of how to use the TPRS3 API with German academic content, which is the primary language for the system.

### 8.1 Complete Workflow Example: Educational Technology Research

#### 8.1.1 Sentiment Analysis Example

**Scenario**: A student wants to research "Künstliche Intelligenz in der Bildung" (Artificial Intelligence in Education)

**Request**:
```json
POST /api/sentiment
{
  "researchTopic": "Künstliche Intelligenz in der Bildung: Chancen und Herausforderungen für das deutsche Schulsystem",
  "researchQuestions": [
    {
      "value": "Wie kann KI-basierte Lernsoftware die individuelle Förderung von Schülern verbessern?"
    },
    {
      "value": "Welche ethischen Bedenken entstehen beim Einsatz von KI-Systemen in deutschen Klassenzimmern?"
    },
    {
      "value": "Wie bereiten deutsche Lehrkräfte sich auf die Integration von KI-Tools vor?"
    }
  ]
}
```

**Expected Response**:
```json
{
  "sentiment": "positive",
  "reason": "Das Forschungsthema zeigt eine ausgewogene Betrachtung von Chancen und Herausforderungen. Die Forschungsfragen sind spezifisch auf den deutschen Kontext ausgerichtet und decken technische, ethische und praktische Aspekte ab.",
  "recommendedEngine": "knowledge_based",
  "timestamp": "2025-07-22T10:30:00Z"
}
```

#### 8.1.2 Research Topic Validation Example

**Request**:
```json
POST /api/research_topic
{
  "researchTopic": "Künstliche Intelligenz in der Bildung: Chancen und Herausforderungen für das deutsche Schulsystem",
  "personal_research_interest": "Ich interessiere mich für die Digitalisierung im Bildungswesen und möchte verstehen, wie neue Technologien das Lernen verbessern können.",
  "summary_of_course": "Modul: Bildungstechnologie und E-Learning. Schwerpunkte: Digitale Lernumgebungen, Adaptive Lernsysteme, Mediendidaktik im 21. Jahrhundert.",
  "llmModel": "llama3.1:8b"
}
```

**Expected Response**:
```json
{
  "researchTopic": "Künstliche Intelligenz in der Bildung: Chancen und Herausforderungen für das deutsche Schulsystem",
  "validate": true,
  "reason": "Das Thema ist hochrelevant und passt ausgezeichnet zu Ihren Interessen und dem Kursinhalt. Es verbindet aktuelle technologische Entwicklungen mit praktischen Bildungsanwendungen und berücksichtigt den spezifischen deutschen Kontext.",
  "timestamp": "2025-07-22T10:35:00Z"
}
```

#### 8.1.3 Research Question Validation Example

**Request**:
```json
POST /api/research_question
{
  "researchTopic": "Künstliche Intelligenz in der Bildung: Chancen und Herausforderungen für das deutsche Schulsystem",
  "researchQuestion": "Wie kann KI-basierte Lernsoftware die individuelle Förderung von Schülern verbessern?",
  "personal_research_interest": "Ich interessiere mich für die Digitalisierung im Bildungswesen und möchte verstehen, wie neue Technologien das Lernen verbessern können.",
  "summary_of_course": "Modul: Bildungstechnologie und E-Learning. Schwerpunkte: Digitale Lernumgebungen, Adaptive Lernsysteme, Mediendidaktik im 21. Jahrhundert.",
  "llmModel": "llama3.1:8b"
}
```

**Expected Response**:
```json
{
  "researchQuestion": "Wie kann KI-basierte Lernsoftware die individuelle Förderung von Schülern verbessern?",
  "validate": true,
  "reason": "Diese Forschungsfrage ist sehr gut formuliert und fokussiert. Sie adressiert einen konkreten Anwendungsbereich (individuelle Förderung) und ist sowohl theoretisch als auch praktisch relevant für Ihr Studienmodul.",
  "timestamp": "2025-07-22T10:40:00Z"
}
```

### 8.2 Psychology Research Example

#### 8.2.1 Mental Health Research Topic

**Scenario**: Research on digital mental health interventions

**Request**:
```json
POST /api/sentiment
{
  "researchTopic": "Digitale Interventionen bei Angststörungen: Wirksamkeit von Smartphone-Apps in der deutschen Gesundheitsversorgung",
  "researchQuestions": [
    {
      "value": "Welche evidenzbasierten Apps zur Angstbewältigung sind in Deutschland verfügbar?"
    },
    {
      "value": "Wie bewerten deutsche Psychotherapeuten den Einsatz digitaler Hilfsmittel?"
    },
    {
      "value": "Welche Datenschutzbestimmungen gelten für Mental-Health-Apps in Deutschland?"
    }
  ]
}
```

**Expected Response**:
```json
{
  "sentiment": "positive",
  "reason": "Das Thema behandelt ein wichtiges gesellschaftliches Problem mit einem innovativen Ansatz. Die Forschungsfragen sind gut strukturiert und berücksichtigen verschiedene Perspektiven: verfügbare Technologien, professionelle Einschätzungen und rechtliche Rahmenbedingungen.",
  "recommendedEngine": "knowledge_based",
  "timestamp": "2025-07-22T11:00:00Z"
}
```

### 8.3 Business Administration Example

#### 8.3.1 Sustainability in German Companies

**Request**:
```json
POST /api/llm_feedback
{
  "researchTopic": "Nachhaltigkeitsberichterstattung in deutschen Mittelstandsunternehmen: Herausforderungen und Best Practices",
  "researchQuestions": [
    {
      "value": "Wie implementieren deutsche KMU die EU-Taxonomie-Verordnung?"
    },
    {
      "value": "Welche Rolle spielt die Nachhaltigkeitsberichterstattung für die Finanzierung von Mittelstandsunternehmen?"
    }
  ],
  "personal_research_interest": "Ich interessiere mich für nachhaltiges Wirtschaften und Corporate Social Responsibility in Deutschland.",
  "summary_of_course": "Modul: Strategisches Management und Nachhaltigkeit. Inhalte: ESG-Kriterien, Nachhaltigkeitsmanagement, Stakeholder-Ansätze.",
  "recommendationEngine": {
    "value": "knowledge_based"
  },
  "llmModel": "llama3.1:8b",
  "references": "Basierend auf aktueller Fachliteratur zu Nachhaltigkeitsberichterstattung und EU-Regulierung."
}
```

**Expected Response** (excerpt):
```json
{
  "llm_feedback": "Ihre Forschungsarbeit zu Nachhaltigkeitsberichterstattung in deutschen Mittelstandsunternehmen ist hochaktuell und praxisrelevant...\n\n**Empfehlungen für Ihre Forschung:**\n\n1. **Methodischer Ansatz**: Kombinieren Sie quantitative Umfragen mit qualitativen Experteninterviews...\n\n2. **Theoretischer Rahmen**: Nutzen Sie die Stakeholder-Theorie und Institutional Theory...\n\n3. **Praktische Relevanz**: Entwickeln Sie konkrete Handlungsempfehlungen für KMU...",
  "input_tokens": 1250,
  "output_tokens": 2100,
  "total_tokens": 3350,
  "timestamp": "2025-07-22T11:15:00Z"
}
```

### 8.4 Engineering Research Example

#### 8.4.1 Renewable Energy Systems

**Request**:
```json
POST /api/chain_of_thought
{
  "researchTopic": "Optimierung von Windkraftanlagen durch maschinelles Lernen: Predictive Maintenance in deutschen Windparks",
  "researchTopicValidation": true,
  "researchTopicValidationReason": "Das Thema verbindet aktuelle KI-Technologien mit praktischen Anwendungen in der Energiewirtschaft.",
  "researchQuestions": [
    {
      "researchQuestion": "Welche Machine-Learning-Algorithmen eignen sich am besten für die Vorhersage von Wartungsbedarf bei Windkraftanlagen?",
      "researchQuestionValidation": true,
      "researchQuestionReason": "Spezifische technische Fragestellung mit klarem Anwendungsbezug."
    }
  ],
  "personal_research_interest": "Ich interessiere mich für die Anwendung von KI in der Energietechnik.",
  "summary_of_course": "Modul: Intelligente Energiesysteme. Schwerpunkte: Smart Grid, Erneuerbare Energien, Datenanalyse.",
  "references": "Aktuelle Forschung zu Predictive Maintenance und Windenergie-Optimierung.",
  "sentiment": "positive",
  "sentimentReason": "Innovative Verbindung von KI und Energietechnik mit hoher Praxisrelevanz.",
  "llm_feedback": "Detaillierte Analyse der technischen Möglichkeiten und Implementierungsstrategien..."
}
```

### 8.5 Common German Academic Phrases and Terminology

The system is optimized to understand and respond to common German academic language:

#### 8.5.1 Research Topic Formulations
- "Analyse der..." (Analysis of...)
- "Einfluss von... auf..." (Influence of... on...)
- "Vergleichende Untersuchung..." (Comparative study...)
- "Entwicklung eines Konzepts für..." (Development of a concept for...)
- "Empirische Studie zu..." (Empirical study on...)

#### 8.5.2 Research Question Patterns
- "Wie wirkt sich... aus?" (How does... affect?)
- "Welche Faktoren beeinflussen...?" (Which factors influence...?)
- "Inwiefern unterscheidet sich...?" (To what extent does... differ?)
- "Welche Rolle spielt... bei...?" (What role does... play in...?)

#### 8.5.3 Academic Context Terms
- "Hausarbeit" (Term paper)
- "Abschlussarbeit" (Thesis)
- "Forschungsinteresse" (Research interest)
- "Modulbeschreibung" (Course description)
- "Literaturrecherche" (Literature review)

---

## 9. Development Workflow

### 7.1 Local Development Setup
1. **Start development environment**:
   ```bash
   ./start-dev.sh
   ```

2. **Monitor logs**:
   ```bash
   docker logs -f fastapi-backend-server
   docker logs -f pgvector-container
   ```

3. **Access interactive API docs**:
   - Swagger UI: http://localhost:8000/docs
   - ReDoc: http://localhost:8000/redoc

### 7.2 Code Structure Guidelines
- **Models**: Define all data structures using Pydantic
- **Endpoints**: Keep endpoint logic minimal, delegate to utilities
- **Utils**: Implement business logic in utility functions
- **Logging**: Use structured logging with appropriate levels
- **Error Handling**: Implement comprehensive exception handling

### 7.3 Testing Workflow
1. **Unit Tests**: Located in `/backend/tests/`
2. **API Testing**: Use FastAPI's test client
3. **Integration Tests**: Test complete workflows
4. **Load Testing**: Verify performance under load

### 7.4 Adding New Features
1. **Define Models**: Create Pydantic models for new data structures
2. **Implement Utils**: Add business logic in utility functions
3. **Create Endpoints**: Add API endpoints with proper documentation
4. **Update Documentation**: Document new features and endpoints
5. **Add Tests**: Implement comprehensive tests

---

## 8. Troubleshooting

### 8.1 Common Issues

#### Container Startup Issues
**Problem**: Containers fail to start
**Solutions**:
- Check port availability: `netstat -tulpn | grep :8000`
- Verify Docker daemon is running: `docker info`
- Check disk space: `df -h`
- Review container logs: `docker logs <container_name>`

#### Database Connection Issues
**Problem**: Cannot connect to PostgreSQL
**Solutions**:
- Verify container is running: `docker ps`
- Check environment variables in `.env` file
- Test connection: `docker exec -it pgvector-container psql -U langchain -d langchain`
- Restart containers: `docker compose down && ./start-dev.sh`

#### Vector Database Issues
**Problem**: Vector insertion fails
**Solutions**:
- Check Ollama server connectivity
- Verify document formats (PDF compatibility)
- Monitor memory usage during insertion
- Check logs for specific error messages

#### LLM Integration Issues
**Problem**: LLM requests fail or timeout
**Solutions**:
- Verify FernUni Ollama server URL
- Check network connectivity
- Monitor request/response sizes
- Implement retry mechanisms

### 8.2 Performance Optimization
- **Database Indexing**: Ensure proper vector indexes
- **Connection Pooling**: Configure PostgreSQL connection pools
- **Caching**: Implement response caching for frequent queries
- **Batch Processing**: Use batch operations for large datasets

### 8.3 Monitoring and Logging
- **Log Levels**: Configure appropriate logging levels
- **Log Rotation**: Implement log rotation to manage disk space
- **Health Checks**: Monitor endpoint health and response times
- **Resource Monitoring**: Track CPU, memory, and disk usage

---

## 9. Deployment Guide

### 9.1 Production Deployment
1. **Environment Setup**:
   ```bash
   # Use production configuration
   ./start-prod.sh
   ```

2. **Environment Variables**:
   - Update `.env` with production values
   - Use secure passwords and connection strings
   - Configure external LLM endpoints

3. **Security Considerations**:
   - Enable HTTPS/TLS encryption
   - Implement authentication and authorization
   - Configure firewall rules
   - Regular security updates

### 9.2 Scaling Considerations
- **Horizontal Scaling**: Deploy multiple backend instances
- **Load Balancing**: Use nginx or cloud load balancers
- **Database Scaling**: Consider read replicas for PostgreSQL
- **Caching Layer**: Implement Redis for response caching

### 9.3 Backup and Recovery
- **Database Backups**: Regular PostgreSQL backups
- **Vector Data**: Backup vector embeddings and indexes
- **Configuration**: Version control for configuration files
- **Disaster Recovery**: Document recovery procedures

---

## 10. Maintenance & Monitoring

### 10.1 Regular Maintenance Tasks
- **Log Cleanup**: Rotate and archive log files
- **Database Maintenance**: Vacuum and analyze PostgreSQL
- **Vector Index Optimization**: Rebuild indexes periodically
- **Dependency Updates**: Keep Python packages updated
- **Security Patches**: Apply system and container updates

### 10.2 Monitoring Metrics
- **API Response Times**: Monitor endpoint performance
- **Database Performance**: Track query execution times
- **Resource Usage**: Monitor CPU, memory, and disk usage
- **Error Rates**: Track and analyze error patterns
- **User Activity**: Monitor API usage patterns

### 10.3 Alerting
- **System Health**: Alert on container failures
- **Performance Degradation**: Alert on slow response times
- **Resource Exhaustion**: Alert on high resource usage
- **Error Spikes**: Alert on increased error rates

---

## Conclusion

This documentation provides a comprehensive guide for understanding, deploying, and maintaining the TPRS 3.0 backend system. The modular architecture and well-documented APIs make it suitable for both development and production environments.

For additional support or questions, contact the development team or refer to the inline code documentation and API schemas available at `/docs` when the server is running.

**Developer**: Rahul Bhoyar (<EMAIL>)
**Version**: 3.0.0
**Last Updated**: 2025-07-22
